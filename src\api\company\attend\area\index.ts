import request from '@/config/axios'

// 查询部门考勤设置明细
export const getAreaDetail = async (deptId: string) => {
  return await request.get({ url: `/system/kq-dept-setting/get/` + deptId })
}
// 新增部门考勤设置
export const createArea = async (data: any) => {
  return await request.post({ url: `/system/kq-dept-setting/create`, data })
}
// 修改部门考勤设置
export const updateArea = async (data: any) => {
  return await request.put({ url: `/system/kq-dept-setting/update`, data })
}
export const deleteArea = async (id: string | number) => {
  return await request.delete({ url: `/system/kq-dept-setting/delete?id=${id}` })
}
