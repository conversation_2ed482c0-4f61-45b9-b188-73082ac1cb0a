import request from '@/config/axios'

export interface GroupRecord {
  pageNo: number,
  pageSize: number,
  month: string,
  day: string,
  deptName: string,
  userName: string,
  startDay: string,
  endDay: string
}
// 获取用户的打卡记录列表
export const getAttendRecordList = async (params: GroupRecord) => {
  return await request.get({ url: `/system/user-attendance/page`, params })
}
// 导出
export const exportFile = async (params: GroupRecord) => {
  return await request.download({ url: `/system/user-attendance/export-excel`, params })
}
