import request from '@/config/axios'

export interface ShiftRecord {
  name: string,
  startTime: string,
  endTime: string,
  color: string,
  id?: string | number
}
// 查询考勤班次列表
export const getShiftList = async (params: {name?: string, pageNo: number, pageSize: number}) => {
  return await request.get({ url: `/system/kq-shift-template/page`, params})
}
// 新增部门考勤设置
export const createShift = async (data: ShiftRecord) => {
  return await request.post({ url: `/system/kq-shift-template/create`, data })
}
// 修改部门考勤设置
export const updateShift = async (data: ShiftRecord) => {
  return await request.put({ url: `/system/kq-shift-template/update`, data })
}
// 删除考勤班次
export const deleteShift = async (id: string | number) => {
  return await request.delete({ url: `/system/kq-shift-template/delete?id=` + id })
}
// 查询考勤班次详情
export const getShift = async (id: string | number) => {
  return await request.get({ url: `/system/kq-shift-template/get?id=` + id })
}
