import request from '@/config/axios'

// ERP 产品 VO
export interface ProductVO {
  id: number // 产品编号
  name: string // 产品名称
  barCode: number // 产品条码-需要盘点
  categoryId: number // 产品类型编号
  unitId: number // 单位编号
  unitName?: string // 单位名字
  status: number // 产品状态
  standard: string // 产品规格
  remark: string // 产品备注
  expiryDay: number // 保质期天数-需要归还
  weight: number // 重量（kg）
  purchasePrice: number // 采购价格，单位：元
  salePrice: number // 销售价格，单位：元
  minPrice: number // 最低价格，单位：元
}
export interface ProductParamsVO {
  productId?: number
  remark?: string
  name?: string
  count?: number
  customerId?: number | null
  type?: number
  bizType?: number
}
export interface CarAddVO {
  companyId: number | undefined
  companyName: string
  deptId: number | undefined
  deptName: string
  type: number
  brand: string
  plateNo: string
  buyTime: string
  price: number
  saUserId: number | undefined
  remark: string
}
export interface CarRecordAddVO {
  companyId: number | undefined
  companyName?: string
  deptId: number | undefined
  deptName?: string
  type: string | undefined
  date: string
  cost: number
  saUserId: number | undefined
  carId: number
  remark: string
  sp: string
}
// ERP 产品 API
export const ProductApi = {
  // 查询产品分页
  getProductPage: async (params: any) => {
    return await request.get({ url: `/erp/product/page`, params })
  },

  // 查询产品精简列表
  getProductSimpleList: async () => {
    return await request.get({ url: `/erp/product/simple-list` })
  },

  // 查询产品详情
  getProduct: async (id: number) => {
    return await request.get({ url: `/erp/product/get?id=` + id })
  },

  // 新增产品
  createProduct: async (data: ProductVO) => {
    return await request.post({ url: `/erp/product/create`, data })
  },

  // 修改产品
  updateProduct: async (data: ProductVO) => {
    return await request.put({ url: `/erp/product/update`, data })
  },

  // 删除产品
  deleteProduct: async (id: number) => {
    return await request.delete({ url: `/erp/product/delete?id=` + id })
  },

  // 导出产品 Excel
  exportProduct: async (params) => {
    return await request.download({ url: `/erp/product/export-excel`, params })
  },
  // 下载模版excel
  exportTemplate: async () => {
    return await request.download({ url: `/erp/product/export-template` })
  },
  // 入库
  productIn: async (data: ProductParamsVO) => {
    return await request.post({ url: `/erp/product/in`, data })
  },
  //出库
  productOut: async (data: ProductParamsVO) => {
    return await request.post({ url: `/erp/product/out`, data })
  },
  //车辆查询
  getCarPage: async (params: any) => {
    return await request.get({ url: `/erp/car/page`, params })
  },
  //车辆新增
  createCar: async (data: CarAddVO) => {
    return await request.post({ url: `/erp/car/create`, data })
  },
  // 修改车辆
  updateCar: async (data: CarAddVO) => {
    return await request.put({ url: `/erp/car/update`, data })
  },
  // 删除车辆
  deleteCar: async (id: number) => {
    return await request.delete({ url: `/erp/car/delete?id=` + id })
  },
  // 查询产品详情
  getCar: async (id: number) => {
    return await request.get({ url: `/erp/car/get?id=` + id })
  },
  //物料-变更记录查询
  recordPage: async (productId: number) => {
    return await request.get({ url: `/erp/product/stock-record-page?productId=` + productId })
  },
  //车辆变更记录详情
  carRecordList: async (id: number) => {
    return await request.get({ url: `/erp/carRecord/get?id=` + id })
  },
  createCarRecord: async (data: CarRecordAddVO) => {
    return await request.post({ url: `/erp/carRecord/create`, data })
  },
  updateCarRecord: async (data: CarRecordAddVO) => {
    return await request.put({ url: `/erp/carRecord/update`, data })
  },
  deleteCarRecord: async (id: number) => {
    return await request.delete({ url: `/erp/carRecord/delete?id=` + id })
  },
  //车辆变更记录列表
  getCarRecordPage: async (params: any) => {
    return await request.get({ url: `/erp/carRecord/page`, params })
  }
}
