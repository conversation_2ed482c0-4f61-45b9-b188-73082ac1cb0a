import request from '@/config/axios'

// 档案 VO
export interface CertificateVO {
  id: number // 编号
  companyId: number // 公司id
  name: string // 名字
  startTime: string // 开始时间
  validTime: string // 开始时间
  state: number // 0 公司；1 借出；2 失效；
  userId: number // 创建人
  isDeleted: number // 1已删除，0未删除
  datatype: number | string // 档案分类
  compno: string // 统一社会信用代码
  compname: string // 公司名称
  type: string // 类型
  person: string // 负责人
  address: string // 营业场所
  certificationUnit: string // 发证单位
  openTime: string // 成立日期
  compStatus: number // 1=在营 2=注销
  remark: string // 备注
  patentPayment: string // 专利付款
  patentPaymentDate: string // 专利付款信息
  honorType: number // 荣誉分类
  createTime: string // 创建时间
  updateTime: string // 更新时间
  creatorName: string // 创建人姓名
}
export interface ceptVO {
  updateSupport: boolean // 是否更新已存在数据
  datatype: string // 档案类型
  file: File // 文件
}
// 档案 VO
export interface BorrowVO {
  certId: string
  userId: number
  username: string | undefined
  operDate: string
  type: string
  remark: string
}
// 档案 API
export const CertificateApi = {
  // 查询档案分页
  getCertificatePage: async (params: any) => {
    return await request.get({ url: `/infra/certificate/page`, params })
  },

  // 查询档案详情
  getCertificate: async (id: number) => {
    return await request.get({ url: `/infra/certificate/get?id=` + id })
  },

  // 新增档案
  createCertificate: async (data: CertificateVO) => {
    return await request.post({ url: `/infra/certificate/create`, data })
  },

  // 修改档案
  updateCertificate: async (data: CertificateVO) => {
    return await request.put({ url: `/infra/certificate/update`, data })
  },

  // 删除档案
  deleteCertificate: async (id: number) => {
    return await request.delete({ url: `/infra/certificate/delete?id=` + id })
  },

  // 导出档案 Excel
  exportCertificate: async (params) => {
    return await request.download({ url: `/infra/certificate/export-excel`, params })
  },

  // 查询荣誉类型
  getHonorType: async () => {
    const params = {
      pageNo: 1,
      pageSize: 20,
      dictType: 'honor_type'
    }
    return await request.get({ url: `/system/dict-data/page`, params })
  },
  // 导入档案 Excel
  importCertificate: async (data: ceptVO) => {
    return await request.post({ url: `/infra/certificate/import`, data })
  },
  // 下载模板
  downloadCertificate: async (datatype: string) => {
    return await request.download({ url: `/infra/certificate/cert/template/${datatype}` })
  },
  // 获取借还记录
  getBorrowPage: async (params: { certId: string }) => {
    return await request.get({ url: `/infra/certificate-record/page`, params })
  },
  // 新增借还
  createBorrowPage: async (data: BorrowVO) => {
    return await request.post({ url: `/infra/certificate-record/create`, data })
  },
  // 更新借还
  updateBorrowPage: async (data: BorrowVO) => {
    return await request.put({ url: `/infra/certificate-record/update`, data })
  },
  // 删除借还
  deleteBorrowPage: async (id: string) => {
    return await request.delete({ url: `/infra/certificate-record/delete?id=` + id })
  },
  // 获取文件列表
  fetchFileList: async (id: string | number) => {
    return await request.get({ url: `/infra/certificate-file/page?certId=` + id })
  },
  // 删除文件
  deleteFile: async (id: string | number) => {
    return await request.delete({ url: `/infra/certificate-file/delete?id=` + id })
  }
}
