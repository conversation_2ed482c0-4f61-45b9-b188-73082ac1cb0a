import request from '@/config/axios'

export interface DeptVO {
  id?: number
  name: string
  parentId: number
  status: number
  sort: number
  leaderUserId: number
  phone: string
  email: string
  createTime: Date
  type: number
}

export interface DeptReward {
  deptId?: number
  // name: string
  // parentId: number
  // status: number
  // sort: number
  // leaderUserId: number
  // phone: string
  // email: string
  // createTime: Date
  rewards:any
}
export interface rewardpageParam {
  pageNo: string
  pageSize: string
  projectId: string
}
// 查询部门（精简)列表
export const getSimpleDeptList = async (): Promise<DeptVO[]> => {
  return await request.get({ url: '/system/dept/simple-list' })
}

// 查询部门列表
export const getDeptPage = async (params: PageParam) => {
  return await request.get({ url: '/system/dept/list', params })
}

// 查询部门详情
export const getDept = async (id: number) => {
  return await request.get({ url: '/system/dept/get?id=' + id })
}

// 新增部门
export const createDept = async (data: DeptVO) => {
  return await request.post({ url: '/system/dept/create', data: data })
}

// 修改部门
export const updateDept = async (params: DeptVO) => {
  return await request.put({ url: '/system/dept/update', data: params })
}

// 删除部门
export const deleteDept = async (id: number) => {
  return await request.delete({ url: '/system/dept/delete?id=' + id })
}
// 获取奖惩列表
export const getRewardList = async (params: rewardpageParam) => {
  return await request.get({ url: '/infra/wages-project-reward/page', params })
}
// 修改奖惩信息
export const updateDeptReward = async (data) => {
  return await request.put({ url: '/infra/wages-project-reward/update', data: data })
}
// 新增奖惩信息
export const createDeptReward = async (data) => {
  return await request.post({ url: '/infra/wages-project-reward/create', data: data })
}

// 获取出勤列表
export const getAttendanceList = async (params: rewardpageParam) => {
  return await request.get({ url: '/infra/wages-project-days/page', params })
}
// 修改出勤信息
export const updateDeptAttendance = async (data) => {
  return await request.put({ url: '/infra/wages-project-days/update', data: data })
}
// 新增出勤信息
export const createDeptAttendance = async (data) => {
  return await request.post({ url: '/infra/wages-project-days/create', data: data })
}
