import request from '@/config/axios'

export interface ProductVO {
  id: number
  name: string
  no: string
  unit: number
  price: number
  status: number
  categoryId: number
  categoryName?: string
  description: string
  ownerUserId: number
}

// 查询产品列表
export const getProductPage = async (params) => {
  return await request.get({ url: `/crm/product/page`, params })
}

// 查询考勤详情
export const getProduct = async (id: number) => {
  return await request.get({ url: `/crm/product/get?id=` + id })
}

// 导入
export const createProduct = async (data: ProductVO) => {
  return await request.post({ url: `/crm/product/create`, data })
}

// 修改考勤
export const updateProduct = async (data: ProductVO) => {
  return await request.put({ url: `/crm/product/update`, data })
}

// 发布
export const exportProduct = async (params) => {
  return await request.download({ url: `/crm/product/export-excel`, params })
}
