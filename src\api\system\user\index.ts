import request from '@/config/axios'

export interface UserVO {
  id?: number | undefined
  username?: string | undefined
  nickname?: string | undefined
  deptId?: number | undefined
  postIds?: string[] | undefined
  email?: string | undefined
  mobile?: string | undefined
  sex?: number | undefined
  avatar?: string | undefined
  loginIp?: string | undefined
  status?: number | undefined
  remark?: string | undefined
  loginDate?: Date | undefined
  createTime?: Date | undefined
  idCard?: string | undefined
  staffNature?: number | undefined
}

// 查询用户管理列表
export const getUserPage = (params: PageParam) => {
  return request.get({ url: '/system/user/page', params })
}

// 查询所有用户列表
export const getAllUser = () => {
  return request.get({ url: '/system/user/all' })
}

// 查询用户详情
export const getUser = (id: number) => {
  return request.get({ url: '/system/user/get?id=' + id })
}

// 新增用户
export const createUser = (data: UserVO) => {
  return request.post({ url: '/system/user/create', data })
}
// 用户离职
export const userResign = (data: any) => {
  return request.post({ url: '/system/userResign/create', data })
}
// 修改用户
export const updateUser = (data: UserVO) => {
  return request.put({ url: '/system/user/update', data })
}

// 删除用户
export const deleteUser = (id: number | undefined) => {
  return request.delete({ url: '/system/user/delete?id=' + id })
}

// 导出用户
export const exportUser = (params) => {
  return request.download({ url: '/system/user/export', params })
}

// 下载用户导入模板
export const importUserTemplate = () => {
  return request.download({ url: '/system/user/get-import-template' })
}

// 下载用户离职导入模板
export const getExportResignTemplate = () => {
  return request.download({ url: '/system/userResign/getExportResignTemplate' })
}
// 用户密码重置
export const resetUserPwd = (id: number | undefined, password: string) => {
  const data = {
    id,
    password
  }
  return request.put({ url: '/system/user/update-password', data: data })
}

// 用户状态修改
export const updateUserStatus = (id: number, status: number) => {
  const data = {
    id,
    status
  }
  return request.put({ url: '/system/user/update-status', data: data })
}

// 获取用户精简信息列表
export const getSimpleUserList = (): Promise<UserVO[]> => {
  return request.get({ url: '/system/user/simple-list' })
}

// 查询用户工资列表/admin-api/infra/wages-user-salary/get?userId=
export const getUserSalary = (id: number) => {
  return request.get({ url: '/infra/wages-user-salary/get?userId=' + id })
}
//所属部门
export const selectDept = (id: number) => {
  return request.get({ url: '/system/userManageDept/selectDept?userId=' + id })
}
// 新增
export const createSalary = (data: any) => {
  return request.post({ url: '/infra/wages-user-salary/create', data })
}

// 修改
export const updateSalary = (data: any) => {
  return request.put({ url: '/infra/wages-user-salary/update', data })
}

// 删除
export const deleteSalary = (id: number) => {
  return request.delete({ url: '/infra/wages-user-salary/delete?id=' + id })
}
// 工伤意外----------------------------
// 查询工伤管理列表
export const getAccidentPage = (params: any) => {
  return request.get({ url: '/system/accident/page', params })
}

// 查询详情
export const getAccident = (id: number) => {
  return request.get({ url: '/system/accident/get?id=' + id })
}

// 新增
export const createAccident = (data: any) => {
  return request.post({ url: '/system/accident/create', data })
}

// 修改
export const updateAccident = (data: any) => {
  return request.put({ url: '/system/accident/update', data })
}

// 删除
export const deleteAccident = (id: number) => {
  return request.delete({ url: '/system/accident/delete?id=' + id })
}

// 导出
export const exportAccident = (params) => {
  return request.download({ url: '/system/accident/export-excel', params })
}

// 下载工伤意外模板
export const importAccidentTemplate = () => {
  return request.download({ url: '/system/accident/export-template' })
}
// 黑名单---------------------

// 查询列表
export const getBlacklistPage = (params: any) => {
  return request.get({ url: '/system/user-blacklist/page', params })
}

// 查询详情
export const getBlacklist = (id: number) => {
  return request.get({ url: '/system/user-blacklist/get?id=' + id })
}

// 新增
export const createBlacklist = (data: any) => {
  return request.post({ url: '/system/user-blacklist/create', data })
}

// 修改
export const updateBlacklist = (data: any) => {
  return request.put({ url: '/system/user-blacklist/update', data })
}

// 删除
export const deleteBlacklist = (id: number) => {
  return request.delete({ url: '/system/user-blacklist/delete?id=' + id })
}

// 员工合同---------------------

// 查询列表
export const getContractPage = (params: any) => {
  return request.get({ url: '/system/contract/page', params })
}

// 查询详情
export const getContract = (id: number) => {
  return request.get({ url: '/system/contract/get?id=' + id })
}

// 新增
export const createContract = (data: any) => {
  return request.post({ url: '/system/contract/create', data })
}

// 修改
export const updateContract = (data: any) => {
  return request.put({ url: '/system/contract/update', data })
}

// 删除
export const deleteContract = (id: number) => {
  return request.delete({ url: '/system/contract/delete?id=' + id })
}

// 导出用户
export const exportContract = (params) => {
  return request.download({ url: '/system/contract/export-excel', params })
}

// 查询合同列表
export const getContractFilePage = (userId: any) => {
  return request.get({ url: '/system/contract-file/page?userId=' + userId })
}

// 删除
export const deleteContractFile = (id: number) => {
  return request.delete({ url: '/system/contract-file/delete?id=' + id })
}

// 查询详情
export const getContractFile = (id: number) => {
  return request.get({ url: '/system/contract-file/get?id=' + id })
}
// 奖惩管理---------------------

// 查询列表
export const getRewardPage = (params: any) => {
  return request.get({ url: '/system/user-reward/page', params })
}

// 查询详情
export const getReward = (id: number) => {
  return request.get({ url: '/system/user-reward/get?id=' + id })
}

// 新增
export const createReward = (data: any) => {
  return request.post({ url: '/system/user-reward/create', data })
}

// 修改
export const updateReward = (data: any) => {
  return request.put({ url: '/system/user-reward/update', data })
}

// 删除
export const deleteReward = (id: number) => {
  return request.delete({ url: '/system/user-reward/delete?id=' + id })
}

// 导出用户
export const exportReward = (params) => {
  return request.download({ url: '/system/user-reward/export-excel', params })
}

// 工作履历管理---------------------
// 查询列表
export const getDeptRecordPage = (params: any) => {
  return request.get({ url: '/system/user-dept-record/page', params })
}
