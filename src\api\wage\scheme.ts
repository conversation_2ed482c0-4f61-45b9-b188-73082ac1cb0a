import request from '@/config/axios'
interface schemeList {
  name: string,
  remark: string,
  socialSecurity: string | number,
  providentFund: string | number,
  commercialInsurance: string | number,
  otherReduce: string | number,
}

// 查询用户管理列表
export const getUserPage = (params: PageParam) => {
  return request.get({ url: '/system/user/page', params })
}

// 创建参保方案
export const createScheme = (data:schemeList) => {
  return request.post({ url: '/infra/wages-insured/create', data })
}

// 查询参保方案列表
export const fetchScheme = (params: PageParam) => {
  return request.get({ url: '/infra/wages-insured/page', params })
}

// 删除参保方案
export const deleteScheme = (id: number) => {
  return request.delete({ url: '/infra/wages-insured/delete?id=' + id })
}

// 查询参保方案明细
export const getScheme = (id) => {
  return request.get({ url: '/infra/wages-insured/get?id=' + id })
}

// 创建工资计算方案
export const createSubsidiaryDetail = (data) => {
  return request.post({ url: '/infra/wages-settlement/create', data })
}

// 查询工资计算方案列表
export const getSubsidiaryDetail = (data) => {
  return request.get({ url: '/infra/wages-settlement/page', data })
}
// 工资计算方案导出
export const exportSubsidiaryDetail = (id, p0: { responseType: string }) => {
  return request.download({ url: '/infra/wages-settlement-user/export-excel?settlementId=' + id })
}

// 删除工资计算方案
export const deleteSubsidiaryDetail = (id) => {
  return request.delete({ url: '/infra/wages-settlement/delete?id=' + id })
}