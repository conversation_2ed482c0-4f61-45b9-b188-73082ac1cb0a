<template>
  <div class="head-container">
    <el-input v-model="deptName" class="mb-20px" clearable placeholder="请输入部门名称">
      <template #prefix>
        <Icon icon="ep:search" />
      </template>
    </el-input>
  </div>
  <div class="head-container" style="height: 730px; width: 300px; overflow: scroll">
    <el-tree
      ref="treeRef"
      :data="deptList"
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
      :props="defaultProps"
      default-expand-all
      highlight-current
      node-key="id"
      style="width: 200%; height: 100%"
      @node-click="handleNodeClick"
      :render-content="renderContent"
    />
  </div>
</template>

<script lang="ts" setup>
import { ElTree, ElIcon } from 'element-plus'
import * as DeptApi from '@/api/system/dept'
import { defaultProps, handleTree } from '@/utils/tree'
import { FolderOpened, Histogram } from '@element-plus/icons-vue'
import { TreeKey } from 'element-plus/es/components/tree/src/tree.type'
defineOptions({ name: 'CeptTree' })
interface DeptVO {
  id?: number
  name: string
  parentId: number
  status: number
  sort: number
  leaderUserId: number
  phone: string
  email: string
  createTime: Date
  type: number
}
const deptName = ref('')
const deptList = ref<Tree[]>([]) // 树形结构
const deptAllList = ref<DeptVO[]>([]) // 树形结构
const treeRef = ref<InstanceType<typeof ElTree>>()

/** 获得部门树 */
const getTree = async () => {
  const res = await DeptApi.getSimpleDeptList()
  deptAllList.value = res
  deptList.value = []
  deptList.value.push(...handleTree(res))
}
const renderContent = (h, { node }) => {
  const type = deptAllList.value.find((item) => item.id === node.key)?.type
  const text = node.data.count ? `${node.label}（${node.data.count}人）` : node.label
  return h('span', [
    // 添加图标
    h(
      ElIcon,
      {
        style: {
          fontSize: '14px',
          verticalAlign: 'middle',
          marginRight: '5px'
        }
      },
      [h(type === 0 ? Histogram : FolderOpened)]
    ),
    h('span', text)
  ])
}
/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}
/** 处理部门被点击 */
const handleNodeClick = async (row: { [key: string]: any }) => {
  emits('node-click', row)
}
const emits = defineEmits(['node-click'])

/** 监听deptName */
watch(deptName, (val) => {
  treeRef.value!.filter(val)
})

/** 初始化 */
onMounted(async () => {
  await getTree()
})
defineExpose({
  getTree,
  setCurrentKey: (key: string | null) => {
    treeRef.value?.setCurrentKey(key as TreeKey | undefined) // 暴露清除点击项的方法
  }
})
</script>

<style></style>
