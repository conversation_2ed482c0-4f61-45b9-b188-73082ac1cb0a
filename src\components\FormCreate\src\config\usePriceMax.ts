import { generateUUID } from '@/utils'
import { localeProps, makeRequiredRule } from '@/components/FormCreate/src/utils'

export const usePriceMax = () => {
  const label = '金额（元）'
  const name = 'PriceMax'
  return {
    icon: 'icon-editor',
    label,
    name,
    rule() {
      return {
        type: 'inputNumber',
        field: 'f'+generateUUID(),
        title: label,
        $required: false,
        suffix: '',
        size: 'large',
        style: {
          width: '100%' // 设置组件宽度
        },
        update: (val, rule) => {
          console.log(254365, rule, val, rule.suffix)
          if (isNaN(val) || val == '') {
            rule.suffix = ''
            return
          }

          const isNegative = val < 0
          const absoluteVal = Math.abs(val)
          const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
          const units = ['', '拾', '佰', '仟']
          const bigUnits = ['', '万', '亿']

          // 处理小数部分
          let decimalPart = ''
          const numStr = absoluteVal.toString()
          if (numStr.includes('.')) {
            const decimalStr = numStr.split('.')[1].substring(0, 2)
            const jiao = decimalStr[0] || '0'
            const fen = decimalStr[1] || '0'

            decimalPart = `${digits[jiao]}角${digits[fen]}分`
            decimalPart = decimalPart
              .replace(/零角零分$/, '整')
              .replace(/零角$/, '整')
              .replace(/零分$/, '')
          } else {
            decimalPart = '整'
          }

          // 处理整数部分
          let integerPart = ''
          const integerStr = Math.floor(absoluteVal).toString()
          const len = integerStr.length

          for (let i = 0; i < len; i++) {
            const n = integerStr[i]
            const unit = units[(len - 1 - i) % 4]
            const bigUnit = bigUnits[Math.floor((len - 1 - i) / 4)]

            if (n === '0') {
              // 处理连续的零
              if (integerPart[integerPart.length - 1] !== '零') {
                integerPart += digits[n]
              }
            } else {
              integerPart += digits[n] + unit
            }

            // 添加大单位
            if ((len - 1 - i) % 4 === 0 && integerPart[integerPart.length - 1] !== '零') {
              integerPart += bigUnit
            }
          }

          // 清理多余的零和大单位
          integerPart = integerPart
            .replace(/零+$/, '')
            .replace(/零万/, '万')
            .replace(/零亿/, '亿')
            .replace(/亿万/, '亿')

          const negativePrefix = isNegative ? '负' : ''
          rule.suffix =
            '大写：' +
            negativePrefix +
            (integerPart ? `${integerPart}元${decimalPart}` : `零元${decimalPart}`)
        }
      }
    },
    props(_, { t }) {
      return localeProps(t, name + '.props', [
        makeRequiredRule(),
        { type: 'switch', field: 'disabled', title: '是否禁用计数器' },
        {
          type: 'inputNumber',
          field: 'min',
          title: '最小值'
        },
        {
          type: 'inputNumber',
          field: 'max',
          title: '最大值'
        },
        {
          type: 'inputNumber',
          title: '小数精度',
          field: 'precision'
        },
        { type: 'inputNumber', field: 'step', props: { min: 0 }, title: '步长' },
        {
          type: 'switch',
          field: 'stepStrictly',
          title: '是否只能输入 step 的倍数'
        },
        {
          type: 'switch',
          field: 'controls',
          value: true,
          title: '是否使用控制按钮'
        }
      ])
    }
  }
}
