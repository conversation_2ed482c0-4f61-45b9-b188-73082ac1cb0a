<template>
  <ContentWrap>
    <div ref="containerRef">
      <!-- 日期选择器 -->
      <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
        end-placeholder="结束日期" @change="generateTable" style="margin-bottom: 16px;" />

      <!-- 加载状态 -->
      <div v-if="loading" style="text-align: center; padding: 20px;">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <span style="margin-left: 8px;">正在加载员工数据...</span>
      </div>

      <!-- 上方：员工排班表（虚拟化） -->
      <el-table-v2 v-loading="loading" :key="tableKey" :columns="employeeTableColumns" :data="tableData" :width="tableWidth"
        :height="300" fixed style="min-width: 600px; margin-bottom: 32px;">
        <template #cell="{ column, rowData, rowIndex }">
          <!-- 如果是班次列，自定义渲染 -->
          <div v-if="column.isShiftColumn"
               :style="getShiftCellStyle(column, rowData, rowIndex)"
               @click="handleShiftCellClick(column, rowData, rowIndex)">
            <!-- 调试信息 -->
            <div style="font-size: 10px; color: red;">
              列索引: {{ column.dayIndex }}, 数据: {{ JSON.stringify(rowData.shifts[column.dayIndex]) }}
            </div>
            <span v-if="rowData.shifts[column.dayIndex]?.name"
                  :style="{ color: '#fff', fontSize: '12px' }">
              {{ rowData.shifts[column.dayIndex].name }}
            </span>
            <span v-else
                  :style="{ color: '#999', fontSize: '14px' }">
              +
            </span>
          </div>
        </template>
      </el-table-v2>

      <!-- 下方：班次人数统计表（虚拟化） -->
      <el-table-v2 :columns="shiftCountTableColumns" :data="shiftCountTable" :width="tableWidth" :height="300" fixed
        style="min-width: 600px; " />

      <!-- 全局唯一班次选择弹窗 -->
      <el-dialog v-model="showShiftDialog" title="选择班次" width="300px" append-to-body>
        <el-button v-for="shift in allShifts" :key="shift.id"
          :style="{ background: shift.color, color: '#fff', margin: '8px 0' }" @click="selectShiftForCurrentCell(shift)"
          block>{{ shift.name }}</el-button>
        <el-button block @click="selectShiftForCurrentCell(restShift)">休息</el-button>
      </el-dialog>
    </div>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, computed, h, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { ElButton, TableV2FixedDir, ElIcon } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import 'element-plus/es/components/table-v2/style/css'
import * as AttendGroupApi from '@/api/company/attend/group'

// 获取路由参数
const route = useRoute()
const groupId = computed(() => route.query.id as string)




// 日期区间
const dateRange = ref<[Date, Date]>([new Date(), new Date()])
const days = ref<{ date: string, label: string }[]>([])

// 主表数据
const tableData = ref<any[]>([])

// 假班次数据
// const allShifts = [
//   { id: 1, name: '10点至下午22点', color: '#409EFF' },
//   { id: 2, name: '12小时夜班', color: '#b37feb' },
//   { id: 3, name: '12小时白班', color: '#36cfc9' }
// ]
//const restShift = { id: -1, name: '休息', color: '#e0e0e0' }
// 定义班次类型
interface ShiftType {
  id: number;
  name: string;
  color: string;
  templatId: number;
  startTime?: string;
  endTime?: string;
}

// 定义工作计划类型
interface WorkPlanType {
  id: number;
  userId: number;
  userName: string;
  date: string;
  templateId: number;
  groupId: number;
  startTime: string;
  endTime: string;
  color: string;
}
const allShifts = ref<ShiftType[]>([])
const restShift = { id: -1, name: '休息', color: '#e0e0e0' }
// 员工数据
const employees = ref<{ id: number; nickname: string }[]>([])
// 加载状态
const loading = ref(false)
// 表格刷新key
const tableKey = ref(0)

// 插槽处理函数

const getShiftCellStyle = (column: any, rowData: any, rowIndex: number) => {
  const shift = rowData.shifts[column.dayIndex]

  const baseStyle = {
    padding: '4px 8px',
    borderRadius: '4px',
    fontSize: '12px',
    textAlign: 'center' as const,
    cursor: 'pointer',
    minHeight: '24px',
    lineHeight: '16px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  }

  if (shift?.name) {
    return {
      ...baseStyle,
      backgroundColor: shift.color || '#409EFF',
      color: '#fff'
    }
  } else {
    return {
      ...baseStyle,
      backgroundColor: '#f5f5f5',
      color: '#999'
    }
  }
}

const handleShiftCellClick = (column: any, rowData: any, rowIndex: number) => {
  console.log(`点击单元格: 行${rowIndex} 列${column.dayIndex}`)
  openShiftDialog(rowIndex, column.dayIndex)
}


// 加载组数据
const loadGroupData = async () => {
  if (groupId.value) {
    console.log('加载组数据，ID:', groupId.value)
    loading.value = true;
    try {
      // 获取员工
      const result = await AttendGroupApi.getGroupUser(groupId.value);
      employees.value = result.map((user: any) => ({
        id: user.id,
        nickname: user.nickname
      }));
      // 获取班次和workPlan
      const normalInfo = await AttendGroupApi.getNormalInfo(groupId.value);
      console.log('获取到的normalInfo:', normalInfo);
      allShifts.value = Array.isArray(normalInfo.normalTime) ? normalInfo.normalTime : [];
      const workPlan = Array.isArray(normalInfo.workPlan) ? normalInfo.workPlan : [];
      console.log('解析后的allShifts:', allShifts.value);
      console.log('解析后的workPlan:', workPlan);

      // 初始化日期区间（如有需要可根据workPlan动态调整）
      // 这里保留原有逻辑
      const today = new Date();
      const end = new Date();
      end.setDate(today.getDate() + 6);
      dateRange.value = [today, end];

      // 生成日期数组
      generateDateRange();

      // 生成完整的表格数据（包含workPlan数据）
      generateCompleteTableData(workPlan);

      console.log('数据加载完成，最终tableData:', tableData.value);

      // 确保DOM更新
      await nextTick();

      // 强制重新渲染表格
      tableKey.value++;
      console.log('DOM更新完成，表格key更新为:', tableKey.value);

      // 验证数据是否正确设置
      tableData.value.forEach((row: any, rowIdx: number) => {
        row.shifts.forEach((shift: any, colIdx: number) => {
          if (shift && shift.name) {
            console.log(`验证: 员工[${rowIdx}] 日期[${colIdx}] 班次:`, shift);
          }
        });
      });
    } catch (error) {
      console.error('加载员工或班次数据失败:', error);
    } finally {
      loading.value = false;
    }
  }
}

// 生成日期范围
const generateDateRange = () => {
  if (!dateRange.value || dateRange.value.length !== 2) return
  const [start, end] = dateRange.value
  const result: { date: string, label: string }[] = []

  let cur = new Date(start)
  const endDate = new Date(end)
  endDate.setHours(0, 0, 0, 0)

  while (cur <= endDate) {
    const m = cur.getMonth() + 1
    const d = cur.getDate()
    const w = ['日', '一', '二', '三', '四', '五', '六'][cur.getDay()]
    const yyyy = cur.getFullYear()
    const mm = String(m).padStart(2, '0')
    const dd = String(d).padStart(2, '0')
    const dateStr = `${yyyy}-${mm}-${dd}`
    result.push({ date: dateStr, label: `${m}/${d}(${w})` })
    cur.setDate(cur.getDate() + 1)
  }

  days.value = result
  console.log('生成的日期数组:', days.value);
}

// 生成完整的表格数据
const generateCompleteTableData = (workPlan: WorkPlanType[]) => {
  console.log('开始生成完整表格数据');
  console.log('员工数据:', employees.value);
  console.log('日期数据:', days.value);
  console.log('工作计划:', workPlan);

  // 初始化表格数据
  tableData.value = employees.value.map(emp => ({
    name: emp.nickname,
    shifts: Array.from({ length: days.value.length }, () => null)
  }));

  console.log('初始化后的tableData:', tableData.value);

  // 填充workPlan数据
  workPlan.forEach((plan: WorkPlanType) => {
    const rowIdx = employees.value.findIndex(emp => emp.id === plan.userId);
    const colIdx = days.value.findIndex(day => day.date === plan.date);

    console.log(`处理计划: 用户${plan.userId} 日期${plan.date} -> 行${rowIdx} 列${colIdx}`);

    if (rowIdx !== -1 && colIdx !== -1) {
      // 查找班次信息
      let shift = allShifts.value.find(s => s.templatId === plan.templateId) as ShiftType | undefined;
      if (!shift) {
        shift = allShifts.value.find(s => s.id === plan.templateId) as ShiftType | undefined;
      }

      const shiftData = {
        ...plan,
        id: plan.templateId,
        name: shift?.name || `班次${plan.templateId}`,
        color: plan.color || shift?.color || '#409EFF',
        templatId: plan.templateId
      };

      console.log(`设置班次数据到 [${rowIdx}][${colIdx}]:`, shiftData);
      tableData.value[rowIdx].shifts[colIdx] = shiftData;
    }
  });

  console.log('最终生成的tableData:', tableData.value);
}

// 统计表数据
const shiftCountTable = computed(() => {
  // 统计每个班次每天的人数
  const arr = [...allShifts.value, restShift].map(shift => ({
    name: shift.name,
    color: shift.color,
    counts: days.value.map((_, dayIdx) => {
      return tableData.value.filter(row => row.shifts[dayIdx]?.id === shift.id).length
    })
  }))
  return arr
})

// 全局唯一弹窗状态
const showShiftDialog = ref(false)
const currentCell = ref<{ rowIdx: number; colIdx: number } | null>(null)

function openShiftDialog(rowIdx: number, colIdx: number) {
  currentCell.value = { rowIdx, colIdx }
  showShiftDialog.value = true
}
async function selectShiftForCurrentCell(shift: any) {
  console.log("shift:", shift);
  if (currentCell.value) {
    tableData.value[currentCell.value.rowIdx].shifts.splice(currentCell.value.colIdx, 1, shift);

    // 获取员工和日期
    const rowIdx = currentCell.value.rowIdx;
    const colIdx = currentCell.value.colIdx;
    const user = employees.value[rowIdx];
    const date = days.value[colIdx]?.date;
    const groupIdVal = groupId.value;
    const color = shift.color;


    //if (shift && shift.id !== -1) {
    const templateId = shift.templatId || shift.id;
    const data = {
      "userId": user.id,
      "userName": user.nickname,
      "date": date,
      "templateId": templateId,
      "groupId": groupIdVal,
      "color": color
    }
    try {
      await AttendGroupApi.rangeWorkCreate(data);
    } catch (e) {
      console.error(e);
    }
    //}
  }
  showShiftDialog.value = false;
}

// 生成表格（根据日期区间）
function generateTable() {
  if (!dateRange.value || dateRange.value.length !== 2) return
  const [start, end] = dateRange.value
  const result: { date: string, label: string }[] = []
  // 只取年月日，忽略时分秒
  let cur = new Date(start)
  const endDate = new Date(end)
  endDate.setHours(0, 0, 0, 0) // 归零时分秒
  while (cur <= endDate) {
    const m = cur.getMonth() + 1
    const d = cur.getDate()
    const w = ['日', '一', '二', '三', '四', '五', '六'][cur.getDay()]
    // 修正：用本地日期格式，避免toISOString带来的时区问题
    const yyyy = cur.getFullYear()
    const mm = String(m).padStart(2, '0')
    const dd = String(d).padStart(2, '0')
    const dateStr = `${yyyy}-${mm}-${dd}`
    result.push({ date: dateStr, label: `${m}/${d}(${w})` })
    cur.setDate(cur.getDate() + 1)
  }
  days.value = result
  console.log('days.value', days.value);

  // 初始化主表 
  const currentEmployees = employees.value.length > 0 ? employees.value : [
  ]

  tableData.value = currentEmployees.map(emp => ({
    name: emp.nickname,
    shifts: Array.from({ length: result.length }, () => null)
  }))
  console.log('generateTable完成，初始化的tableData:', tableData.value);
}



// 虚拟表格列定义（员工排班表）
const employeeTableColumns = computed(() => {
  const cols: any[] = [
    {
      key: 'name',
      title: '姓名',
      dataKey: 'name',
      width: 80,
      fixed: TableV2FixedDir.LEFT,
      cellRenderer: ({ cellData }: any) => cellData
    }
  ]
  days.value.forEach((day, idx) => {
    const [, , d] = day.date.split('-')
    const week = day.label.match(/\((.+)\)$/)?.[1] || ''
    cols.push({
      key: 'day-' + idx,
      title: h('div', { style: { lineHeight: '1.1', textAlign: 'center' } }, [
        h('div', { style: { fontSize: '12px' } }, `${parseInt(d, 10)}`),
        h('div', { style: { fontSize: '12px', color: '#888' } }, `周${week}`)
      ]),
      width: 60, // 适配宽度
      dataKey: 'shifts', // 数据键
      isShiftColumn: true, // 标识这是班次列
      dayIndex: idx // 保存日期索引
    })
  })
  return cols
})

// 班次停机表格
const shiftCountTableColumns = computed(() => {
  const cols: any[] = [
    {
      key: 'name',
      title: '班次',
      dataKey: 'name',
      width: 80,
      fixed: TableV2FixedDir.LEFT,
      cellRenderer: ({ cellData }: any) => cellData
    }
  ]
  days.value.forEach((day, idx) => {
    const [, , d] = day.date.split('-')
    const week = day.label.match(/\((.+)\)$/)?.[1] || ''
    cols.push({
      key: 'count-' + idx,
      title: h('div', { style: { lineHeight: '1.1', textAlign: 'center' } }, [
        h('div', { style: { fontSize: '12px' } }, `${parseInt(d, 10)}`),
        h('div', { style: { fontSize: '12px', color: '#888' } }, `周${week}`)
      ]),
      width: 50,
      cellRenderer: ({ rowData }: any) => h('div', { style: { fontSize: '12px', textAlign: 'center', width: '25px' } }, rowData.counts[idx])
    })
  })
  return cols
})

// 监听最外层div宽度
const containerRef = ref<HTMLElement | null>(null)
const containerWidth = ref(800) // 默认宽度
const containerHeight = ref(800) // 默认高度

let resizeObserver: ResizeObserver | null = null

onMounted(async () => {
  // 加载组数据
  await loadGroupData()

  // 初始化日期
  const today = new Date()
  const end = new Date()
  end.setDate(today.getDate() + 6)
  dateRange.value = [today, end]

  // 生成表格（在员工数据加载完成后）
  generateTable()

  if (containerRef.value) {
    // 初始赋值
    containerWidth.value = containerRef.value.clientWidth
    resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        if (entry.contentRect) {
          containerWidth.value = entry.contentRect.width
          containerHeight.value = entry.contentRect.height
        }
      }
    })
    resizeObserver.observe(containerRef.value)
  }
})

onBeforeUnmount(() => {
  if (resizeObserver && containerRef.value) {
    resizeObserver.unobserve(containerRef.value)
  }
})

// 虚拟表格宽度
const tableWidth = computed(() => containerWidth.value)

</script>
